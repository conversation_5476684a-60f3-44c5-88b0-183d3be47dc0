import type { NextApiRequest, NextApiResponse } from 'next';
import { createSupabaseServerClient } from '../utils/supabase';
import type { Database } from '@/types/supabase';
import type { CampaignTags } from '@/types/campaigns';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<CampaignTags[] | { message: string; error?: string }>
) {
  try {
    const supabase = await createSupabaseServerClient();

    const { data: campaigns, error } = await supabase
      .from('campaigns')
      .select('id, data')
      .eq('active', true);

    if (error) {
      console.error('Supabase error:', error);
      return res.status(500).json({ message: 'Error fetching campaigns', error: error.message });
    }

    if (!campaigns) {
      return res.status(200).json([]);
    }

    // Sort campaigns by UPD funding amount (highest first), then by ID (lowest first)
    const sortedCampaigns = campaigns.sort((a, b) => {
      // Get UPD funding amounts
      const aUpdFunding = a.data.funding?.find((f: any) => f.token === 'UPD')?.amount || 0;
      const bUpdFunding = b.data.funding?.find((f: any) => f.token === 'UPD')?.amount || 0;

      // Primary sort: UPD funding amount (highest first)
      if (aUpdFunding !== bUpdFunding) {
        return bUpdFunding - aUpdFunding;
      }

      // Secondary sort: ID (lowest first)
      return a.id - b.id;
    });

    // Create campaign tags from sorted campaigns
    const campaignTags: CampaignTags[] = sortedCampaigns.map((campaign) => {
      return {
        id: campaign.id,
        name: campaign.data.name,
        tags: campaign.data.tags,
      };
    });

    res.status(200).json(campaignTags);
  } catch (error: any) {
    console.error('API route error:', error);
    res.status(500).json({ message: 'Internal Server Error', error: error.message });
  }
}
