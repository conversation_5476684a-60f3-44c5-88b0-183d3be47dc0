import type { NextApiRequest, NextApiResponse } from 'next';
import { createSupabaseServerClient } from '../utils/supabase';
import type { Database } from '@/types/supabase';
import type campaign from '@/types/campaign-schema.json';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<DropdownOption[] | { message: string; error?: string }> // Still type the final response array
) {
  try {
    const supabase = await createSupabaseServerClient();

    const { data: campaigns, error } = await supabase
      .from('campaigns')
      .select('id, data') // Fetch the ID and the raw JSONB 'data' column
      .eq('is_active', true)
      .order('data->name', { ascending: true }); // fix this to order by amount of UPD token funding, not name

    if (error) {
      /* ... */
    }

    // 'campaigns' is now CampaignRow[], where each 'campaign.data' is strongly typed
    const dropdownOptions: DropdownOption[] = campaigns.map((campaign) => {
      // No more type assertion needed for campaign.data!
      return {
        value: campaign.id,
        label: campaign.data.name, // Autocompletion and type checking here!
        tags: campaign.data.tags,
      };
    });

    res.status(200).json(dropdownOptions);
  } catch (error: any) {
    /* ... */
  }
}
