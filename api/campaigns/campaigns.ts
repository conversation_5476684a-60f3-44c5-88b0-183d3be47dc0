import { NextApiRequest, NextApiResponse } from 'next';
import { createSupabaseServerClient } from '../utils/supabase';
import { Database } from '@/types/supabase';

// Define the expected type of a campaign row
type CampaignRow = Database['public']['Tables']['campaigns']['Row'];

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<CampaignRow[] | { message: string; error?: string }> // Type the response
) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  try {
    const supabase = await createSupabaseServerClient();

    // Fetch all active campaigns
    const { data: campaigns, error } = await supabase
      .from('campaigns')
      .select('*')
      .eq('active', true);

    if (error) {
      console.error('Supabase error:', error);
      return res.status(500).json({ message: 'Error fetching campaigns', error: error.message });
    }

    if (!campaigns) {
      return res.status(200).json([]);
    }

    // Sort campaigns by UPD funding amount (highest first), then by ID (lowest first)
    const sortedCampaigns = campaigns.sort((a, b) => {
      // Get UPD funding amounts
      const aUpdFunding = a.data.funding?.find((f: any) => f.token === 'UPD')?.amount || 0;
      const bUpdFunding = b.data.funding?.find((f: any) => f.token === 'UPD')?.amount || 0;

      // Primary sort: UPD funding amount (highest first)
      if (aUpdFunding !== bUpdFunding) {
        return bUpdFunding - aUpdFunding;
      }

      // Secondary sort: ID (lowest first)
      return a.id - b.id;
    });

    res.status(200).json(sortedCampaigns);
  } catch (error: any) { // Catch as any or unknown and refine
    console.error('API route error:', error);
    res.status(500).json({ message: 'Internal Server Error', error: error.message });
  }
}