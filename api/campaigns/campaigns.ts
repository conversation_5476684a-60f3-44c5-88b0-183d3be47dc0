// pages/api/campaigns/active.ts
import { NextApiRequest, NextApiResponse } from 'next';
import { createSupabaseServerClient } from '../../../src/utils/supabase/server'; // Adjust path
import { Database } from '../../../src/types/supabase'; // Import your generated types

// Define the expected type of a campaign row
type CampaignRow = Database['public']['Tables']['campaigns']['Row'];

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<CampaignRow[] | { message: string; error?: string }> // Type the response
) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  try {
    const supabase = await createSupabaseServerClient();

    // The 'data' variable will now be correctly typed as CampaignRow[]
    const { data: campaigns, error } = await supabase
      .from('campaigns')
      .select('*') // Select all columns, including your JSONB 'data' column
      .eq('is_active', true)
      .order('created_at', { ascending: false }); // Example ordering

    if (error) {
      console.error('Supabase error:', error);
      return res.status(500).json({ message: 'Error fetching campaigns', error: error.message });
    }

    // `campaigns` is already correctly typed, so you can send it directly
    res.status(200).json(campaigns);
  } catch (error: any) { // Catch as any or unknown and refine
    console.error('API route error:', error);
    res.status(500).json({ message: 'Internal Server Error', error: error.message });
  }
}