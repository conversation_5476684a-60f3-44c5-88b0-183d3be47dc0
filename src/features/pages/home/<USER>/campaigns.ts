import { customElement, state } from 'lit/decorators.js';
import { css, LitElement } from 'lit';
import { SignalWatcher, html } from '@lit-labs/signals';

// Shoelace components
import '@shoelace-style/shoelace/dist/components/card/card';
import '@shoelace-style/shoelace/dist/components/button/button';

// Types
import type { Database } from '@/types/supabase';

type CampaignRow = Database['public']['Tables']['campaigns']['Row'];

@customElement('campaigns')
export class Campaigns extends SignalWatcher(LitElement) {
  @state() private campaigns: CampaignRow[] = [];
  @state() private loading = true;
  @state() private error: string | null = null;

  static styles = css`
    h2 {
      margin: 1.5rem 0 1rem;
    }
    section {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 1.25rem;
      margin-bottom: 1.25rem;
    }
    sl-card {
      --padding: 1.25rem;
    }
    sl-card h3 {
      font-size: 1.25rem;
      font-weight: 600;
      margin: 0 0 1rem 0;
    }
    sl-card p {
      font-size: 0.875rem;
      line-height: 1.5;
    }
    sl-card::part(base),
    sl-card::part(body) {
      height: 100%;
    }
    sl-card img {
      float: right;
      width: 125px;
      height: auto;
      margin: 0 0 8px 16px;
    }
    .clear-float {
      clear: both;
    }
    sl-card.campaign {
      position: relative;
      --border-radius: 6px 12px 6px 6px;
    }
    sl-card.campaign::part(base) {
      background: linear-gradient(
        135deg,
        var(--sl-color-neutral-0) 0%,
        var(--sl-color-primary-50) 100%
      );
      border: 2px solid var(--border-accent);
    }
    sl-card.campaign::before {
      content: '🚀 Campaign';
      position: absolute;
      top: 0px;
      right: 0px;
      background: var(--badge);
      color: var(--badge-text);
      padding: 0.25rem 0.75rem;
      border-radius: 0 10px;
      font-size: 0.65rem;
      font-weight: 600;
      text-transform: uppercase;
      z-index: 1;
    }
    sl-card.campaign h3 {
      color: var(--accent);
      font-weight: 700;
    }
    sl-card.campaign > h4 {
      font-size: 1rem;
      font-weight: 600;
      margin: 1rem 0 0.5rem 0;
      color: var(--subtle-text);
      border-bottom: 2px solid var(--border-accent);
      padding: 0.25rem;
      max-width: 50%;
    }
    sl-card.campaign .committed-list {
      margin: 0.5rem 0 1rem 0;
      padding-left: 1.25rem;
    }
    .committed-list li {
      font-size: 0.875rem;
      font-weight: 500;
      color: var(--main-foreground);
      margin-bottom: 0.25rem;
    }
    .tags {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      gap: 0.25rem;
      padding: 0;
      list-style: none;
      margin: 0;
    }
    .tags h4 {
      font-size: 0.85rem;
      margin: 0 0.25rem 0 0;
      padding: 0.25rem 0 0;
    }
    .tags li {
      background: var(--subtle-background);
      color: var(--main-foreground);
      padding: 0.25rem 0.5rem;
      margin-top: 0.25rem;
      border-radius: 6px;
      font-size: 0.75rem;
      font-weight: 500;
    }
    .loading {
      text-align: center;
      padding: 2rem;
      color: var(--subtle-text);
    }
    .error {
      text-align: center;
      padding: 2rem;
      color: var(--error-text);
    }
    .no-campaigns {
      text-align: center;
      padding: 2rem;
      color: var(--subtle-text);
    }
  `;

  async connectedCallback() {
    super.connectedCallback();
    await this.fetchCampaigns();
  }

  private async fetchCampaigns() {
    try {
      this.loading = true;
      this.error = null;
      
      const response = await fetch('/api/campaigns/campaigns');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      if (Array.isArray(data)) {
        this.campaigns = data;
      } else if (data.message) {
        throw new Error(data.message);
      }
    } catch (error) {
      console.error('Error fetching campaigns:', error);
      this.error = error instanceof Error ? error.message : 'Failed to load campaigns';
    } finally {
      this.loading = false;
    }
  }

  private formatAmount(amount: number): string {
    if (amount >= 1000000) {
      return `${(amount / 1000000).toFixed(1)}M`;
    } else if (amount >= 1000) {
      return `${(amount / 1000).toFixed(1)}K`;
    }
    return amount.toString();
  }

  private renderCampaign(campaign: CampaignRow) {
    const data = campaign.data;
    const searchTags = data.tags.map(tag => `[${tag}]`).join(' ');
    
    return html`
      <sl-card class="campaign">
        ${data.image?.url ? html`<img src=${data.image.url} alt=${data.image.alt || data.name} />` : ''}
        <h3>${data.name}</h3>
        <p>
          ${data.description}
          ${data.link ? html`
            <a href=${data.link.url} target="_blank">${data.link.text}</a>
          ` : ''}
        </p>
        ${data.funding && data.funding.length > 0 ? html`
          <h4>Committed</h4>
          <ul class="committed-list">
            ${data.funding.map(funding => html`
              <li>${this.formatAmount(funding.amount)} ${funding.token}</li>
            `)}
          </ul>
        ` : ''}
        <div class="clear-float"></div>
        <ul class="tags">
          <h4>Tags:</h4>
          ${data.tags.map(tag => html`<li>${tag}</li>`)}
        </ul>
        <sl-button
          slot="footer"
          variant="primary"
          href="/discover?search=${searchTags}"
        >
          Ideas with these tags
        </sl-button>
      </sl-card>
    `;
  }

  render() {
    if (this.loading) {
      return html`<div class="loading">Loading campaigns...</div>`;
    }

    if (this.error) {
      return html`<div class="error">Error: ${this.error}</div>`;
    }

    if (this.campaigns.length === 0) {
      return html`<div class="no-campaigns">No active campaigns found.</div>`;
    }

    return html`
      <h2>Campaigns</h2>
      <section>
        ${this.campaigns.map(campaign => this.renderCampaign(campaign))}
      </section>
    `;
  }
}

declare global {
  interface HTMLElementTagNameMap {
    'campaigns': Campaigns;
  }
}
