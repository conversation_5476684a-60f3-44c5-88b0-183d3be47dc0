export type CampaignData = {
  name: string;
  description?: string;
  image?: {
    url: string;
    alt?: string;
  };
  link?: {
    url: string;
    text: string;
  };
  funding?: Array<{
    token: string;
    amount: number;
  }>;
  tags: string[];
  active?: boolean;
};

export type CampaignRow = {
  id: number;
  active: boolean | null;
  data: CampaignData;
};

export type CampaignTags = {
  id: number;
  name: string;
  tags: string[];
};
